<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroTech Product Catalog 2025 - Professional Agricultural Equipment</title>
    <style>
        /* A4 Print Styles */
        @page {
            size: A4;
            margin: 15mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
            background: white;
        }
        
        /* Print-specific styles */
        @media print {
            body { -webkit-print-color-adjust: exact; }
            .page-break { page-break-before: always; }
            .no-print { display: none; }
        }
        
        /* Header */
        .brochure-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 3px solid #2E7D32;
            padding-bottom: 15px;
        }
        
        .company-logo {
            font-size: 28px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 5px;
        }
        
        .company-tagline {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .catalog-title {
            font-size: 18px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 5px;
        }
        
        .catalog-subtitle {
            font-size: 11px;
            color: #666;
        }
        
        /* Category Sections */
        .category-section {
            margin-bottom: 25px;
        }
        
        .category-header {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .category-icon {
            margin-right: 8px;
        }
        
        /* Product Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .product-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            background: #fafafa;
            position: relative;
        }
        
        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .product-name {
            font-size: 11px;
            font-weight: bold;
            color: #2E7D32;
            line-height: 1.2;
            flex: 1;
        }
        
        .product-badge {
            background: #FF6B35;
            color: white;
            font-size: 7px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        .product-brand {
            font-size: 8px;
            color: #666;
            margin-bottom: 6px;
        }
        
        .product-description {
            font-size: 8px;
            color: #555;
            line-height: 1.3;
            margin-bottom: 8px;
            height: 32px;
            overflow: hidden;
        }
        
        .product-specs {
            margin-bottom: 8px;
        }
        
        .spec-row {
            display: flex;
            justify-content: space-between;
            font-size: 7px;
            margin-bottom: 2px;
        }
        
        .spec-label {
            color: #666;
            font-weight: 500;
        }
        
        .spec-value {
            color: #333;
            font-weight: bold;
        }
        
        .product-features {
            margin-bottom: 8px;
        }
        
        .features-title {
            font-size: 8px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 3px;
        }
        
        .feature-item {
            font-size: 7px;
            color: #555;
            margin-bottom: 1px;
            padding-left: 8px;
            position: relative;
        }
        
        .feature-item::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #eee;
            padding-top: 6px;
        }
        
        .product-price {
            font-size: 9px;
            font-weight: bold;
            color: #2E7D32;
        }
        
        .product-rating {
            font-size: 7px;
            color: #666;
        }
        
        .rating-stars {
            color: #FFD700;
            margin-right: 3px;
        }
        
        /* Footer */
        .brochure-footer {
            margin-top: 30px;
            border-top: 2px solid #2E7D32;
            padding-top: 15px;
            text-align: center;
        }
        
        .contact-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .contact-item {
            text-align: center;
        }
        
        .contact-icon {
            font-size: 16px;
            color: #2E7D32;
            margin-bottom: 5px;
        }
        
        .contact-label {
            font-size: 8px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .contact-value {
            font-size: 9px;
            font-weight: bold;
            color: #333;
        }
        
        .marketing-credit {
            font-size: 8px;
            color: #666;
            margin-top: 10px;
        }
        
        .marketing-credit strong {
            color: #2E7D32;
        }
        
        /* Print button */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2E7D32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #1e5e23;
        }
        
        /* Category specific colors */
        .processing { border-left: 4px solid #2E7D32; }
        .protection { border-left: 4px solid #4CAF50; }
        .maintenance { border-left: 4px solid #FF6B35; }
        .power { border-left: 4px solid #1565C0; }
        .irrigation { border-left: 4px solid #00BCD4; }
        .transport { border-left: 4px solid #9C27B0; }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">🖨️ Print Brochure</button>
    
    <!-- Header -->
    <div class="brochure-header">
        <div class="company-logo">🌾 a.agrotech</div>
        <div class="company-tagline">Advanced Agricultural Equipment for Modern Farming</div>
        <div class="catalog-title">Product Catalog 2025</div>
        <div class="catalog-subtitle">Professional-Grade Agricultural Machinery & Equipment</div>
    </div>
    
    <!-- Processing Equipment Section -->
    <div class="category-section">
        <div class="category-header">
            <span class="category-icon">⚙️</span>Processing Equipment
        </div>
        <div class="products-grid" id="processing-products"></div>
    </div>
    
    <!-- Crop Protection Section -->
    <div class="category-section page-break">
        <div class="category-header">
            <span class="category-icon">🌱</span>Crop Protection Equipment
        </div>
        <div class="products-grid" id="protection-products"></div>
    </div>
    
    <!-- Maintenance Equipment Section -->
    <div class="category-section">
        <div class="category-header">
            <span class="category-icon">🔧</span>Maintenance Equipment
        </div>
        <div class="products-grid" id="maintenance-products"></div>
    </div>
    
    <!-- Power Generation Section -->
    <div class="category-section page-break">
        <div class="category-header">
            <span class="category-icon">⚡</span>Power Generation Equipment
        </div>
        <div class="products-grid" id="power-products"></div>
    </div>
    
    <!-- Other Equipment Section -->
    <div class="category-section">
        <div class="category-header">
            <span class="category-icon">🚜</span>Other Agricultural Equipment
        </div>
        <div class="products-grid" id="other-products"></div>
    </div>
    
    <!-- Footer -->
    <div class="brochure-footer">
        <div class="contact-info">
            <div class="contact-item">
                <div class="contact-icon">📞</div>
                <div class="contact-label">Call Us</div>
                <div class="contact-value">+91 9876543210</div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">📧</div>
                <div class="contact-label">Email</div>
                <div class="contact-value"><EMAIL></div>
            </div>
            <div class="contact-item">
                <div class="contact-icon">🌐</div>
                <div class="contact-label">Website</div>
                <div class="contact-value">www.agrotech.com</div>
            </div>
        </div>
        <div class="marketing-credit">
            Marketed by <strong>Artheon E-commerce Pvt Ltd</strong> | Professional Agricultural Solutions Since 2020
        </div>
    </div>

    <script>
        // Load and display products
        async function loadProducts() {
            try {
                const response = await fetch('data/products.json');
                const data = await response.json();
                displayProducts(data.products);
            } catch (error) {
                console.error('Error loading products:', error);
                // Fallback: use embedded product data if fetch fails
                displayProducts(fallbackProducts);
            }
        }

        function displayProducts(products) {
            // Categorize products
            const categories = {
                processing: [],
                protection: [],
                maintenance: [],
                power: [],
                other: []
            };

            products.forEach(product => {
                switch(product.category) {
                    case 'processing':
                        categories.processing.push(product);
                        break;
                    case 'protection':
                        categories.protection.push(product);
                        break;
                    case 'maintenance':
                        categories.maintenance.push(product);
                        break;
                    case 'power':
                        categories.power.push(product);
                        break;
                    default:
                        categories.other.push(product);
                }
            });

            // Render each category
            renderCategory('processing-products', categories.processing, 'processing');
            renderCategory('protection-products', categories.protection, 'protection');
            renderCategory('maintenance-products', categories.maintenance, 'maintenance');
            renderCategory('power-products', categories.power, 'power');
            renderCategory('other-products', categories.other, 'other');
        }

        function renderCategory(containerId, products, categoryClass) {
            const container = document.getElementById(containerId);
            if (!container) return;

            container.innerHTML = products.map(product => createProductCard(product, categoryClass)).join('');
        }

        function createProductCard(product, categoryClass) {
            const badge = product.badge ? `<div class="product-badge">${product.badge}</div>` : '';
            const rating = '★'.repeat(Math.floor(product.rating)) + '☆'.repeat(5 - Math.floor(product.rating));

            // Get key specifications
            const keySpecs = getKeySpecs(product.specifications);

            // Get top 3 features
            const topFeatures = product.features.slice(0, 3);

            return `
                <div class="product-card ${categoryClass}">
                    <div class="product-header">
                        <div class="product-name">${product.name}</div>
                        ${badge}
                    </div>
                    <div class="product-brand">Brand: ${product.brand}</div>
                    <div class="product-description">${product.description}</div>

                    <div class="product-specs">
                        ${keySpecs.map(spec => `
                            <div class="spec-row">
                                <span class="spec-label">${spec.label}:</span>
                                <span class="spec-value">${spec.value}</span>
                            </div>
                        `).join('')}
                    </div>

                    <div class="product-features">
                        <div class="features-title">Key Features:</div>
                        ${topFeatures.map(feature => `
                            <div class="feature-item">${feature}</div>
                        `).join('')}
                    </div>

                    <div class="product-footer">
                        <div class="product-price">${product.priceRange}</div>
                        <div class="product-rating">
                            <span class="rating-stars">${rating}</span>
                            (${product.reviews} reviews)
                        </div>
                    </div>
                </div>
            `;
        }

        function getKeySpecs(specifications) {
            const specs = [];
            const specKeys = Object.keys(specifications);

            // Prioritize important specifications
            const priorityKeys = ['Power Required', 'Processing Capacity', 'Tank Capacity', 'Power Output', 'Engine', 'Capacity'];

            priorityKeys.forEach(key => {
                if (specifications[key]) {
                    specs.push({ label: key, value: specifications[key] });
                }
            });

            // Add other specs if we have space (max 3)
            specKeys.forEach(key => {
                if (specs.length < 3 && !priorityKeys.includes(key)) {
                    specs.push({ label: key, value: specifications[key] });
                }
            });

            return specs.slice(0, 3);
        }

        // Fallback product data (embedded for offline use)
        const fallbackProducts = [
            {
                "id": 1, "name": "Arecanut Dehusking Machine", "brand": "AgroTech", "category": "processing",
                "description": "Professional-grade arecanut dehusking machine designed for efficient processing of betel nuts.",
                "features": ["High-efficiency dehusking mechanism", "Robust steel construction", "Adjustable processing speed"],
                "specifications": {"Processing Capacity": "200-300 kg/hour", "Power Required": "5 HP Electric Motor", "Machine Weight": "350 kg"},
                "priceRange": "₹1.8 - 2.2 Lakhs", "badge": "Best Seller", "rating": 4.6, "reviews": 89
            }
            // Additional fallback products would be added here if needed
        ];

        // Load products when page loads
        document.addEventListener('DOMContentLoaded', loadProducts);
    </script>
</body>
</html>
