<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fixes Test - a.agrotech</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #218838;
        }
        #console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>JavaScript Fixes Test - a.agrotech</h1>
    <p>This page tests the JavaScript fixes for the agricultural B2B website.</p>

    <div class="test-section">
        <h2>1. setupFuzzySearch Function Test</h2>
        <p>Testing if the setupFuzzySearch function is properly defined and accessible.</p>
        <button onclick="testSetupFuzzySearch()">Test setupFuzzySearch</button>
        <div id="fuzzy-search-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Mobile View Test</h2>
        <p>Testing mobile view initialization and element detection.</p>
        <button onclick="testMobileView()">Test Mobile View (320px)</button>
        <button onclick="testDesktopView()">Test Desktop View (1024px)</button>
        <div id="mobile-view-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Search Functions Test</h2>
        <p>Testing search functionality and product database loading.</p>
        <button onclick="testSearchFunctions()">Test Search Functions</button>
        <div id="search-functions-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Error Handling Test</h2>
        <p>Testing error handling and graceful degradation.</p>
        <button onclick="testErrorHandling()">Test Error Handling</button>
        <div id="error-handling-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <p>Real-time console output from the tests:</p>
        <div id="console-output"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');

        function logToPage(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage('log', ...args);
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage('error', ...args);
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPage('warn', ...args);
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function testSetupFuzzySearch() {
            const resultDiv = document.getElementById('fuzzy-search-result');
            try {
                if (typeof setupFuzzySearch === 'function') {
                    setupFuzzySearch();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ SUCCESS: setupFuzzySearch function is defined and callable';
                } else if (typeof window.setupFuzzySearch === 'function') {
                    window.setupFuzzySearch();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ SUCCESS: setupFuzzySearch function is available on window object';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = '✗ ERROR: setupFuzzySearch function is not defined';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ERROR: ${error.message}`;
            }
        }

        function testMobileView() {
            const resultDiv = document.getElementById('mobile-view-result');
            try {
                // Simulate mobile width
                Object.defineProperty(window, 'innerWidth', {
                    writable: true,
                    configurable: true,
                    value: 320
                });

                if (typeof initMobileProductView === 'function') {
                    initMobileProductView();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ SUCCESS: Mobile view initialization completed without errors';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = '✗ ERROR: initMobileProductView function not found';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ERROR: ${error.message}`;
            }
        }

        function testDesktopView() {
            const resultDiv = document.getElementById('mobile-view-result');
            try {
                // Simulate desktop width
                Object.defineProperty(window, 'innerWidth', {
                    writable: true,
                    configurable: true,
                    value: 1024
                });

                if (typeof initMobileProductView === 'function') {
                    initMobileProductView();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = '✓ SUCCESS: Desktop view initialization completed without errors';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = '✗ ERROR: initMobileProductView function not found';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ERROR: ${error.message}`;
            }
        }

        function testSearchFunctions() {
            const resultDiv = document.getElementById('search-functions-result');
            try {
                let successCount = 0;
                let totalTests = 0;

                // Test if search functions are available
                const functionsToTest = [
                    'performSearch',
                    'fuzzySearch',
                    'selectProduct',
                    'redirectToSearchResults',
                    'clearSearchResults'
                ];

                functionsToTest.forEach(funcName => {
                    totalTests++;
                    if (window.searchFunctions && typeof window.searchFunctions[funcName] === 'function') {
                        successCount++;
                        console.log(`✓ ${funcName} function is available`);
                    } else {
                        console.error(`✗ ${funcName} function is missing`);
                    }
                });

                if (successCount === totalTests) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✓ SUCCESS: All ${totalTests} search functions are available`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `✗ PARTIAL: ${successCount}/${totalTests} search functions are available`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ERROR: ${error.message}`;
            }
        }

        function testErrorHandling() {
            const resultDiv = document.getElementById('error-handling-result');
            try {
                // Test error handling by calling functions with invalid parameters
                let errorsCaught = 0;
                let totalTests = 0;

                // Test 1: Call fuzzy search with invalid data
                totalTests++;
                try {
                    if (window.searchFunctions && window.searchFunctions.fuzzySearch) {
                        window.searchFunctions.fuzzySearch('test', null);
                    }
                } catch (e) {
                    errorsCaught++;
                    console.log('✓ Error handling working for fuzzy search with null data');
                }

                // Test 2: Call mobile view with missing elements
                totalTests++;
                try {
                    if (typeof initMobileProductView === 'function') {
                        initMobileProductView();
                        console.log('✓ Mobile view handles missing elements gracefully');
                    }
                } catch (e) {
                    console.error('✗ Mobile view error handling failed:', e.message);
                }

                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✓ SUCCESS: Error handling tests completed - check console for details';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ERROR: ${error.message}`;
            }
        }

        // Run initial tests when page loads
        window.addEventListener('load', function() {
            console.log('Test page loaded, running initial tests...');
            setTimeout(() => {
                testSetupFuzzySearch();
                testSearchFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
