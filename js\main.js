// Main JavaScript functionality for a.agrotech website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initSearch();
    initFeaturedProducts();
    initScrollAnimations();
    initFormValidation();
    initTooltips();
    initLazyLoading();
});

// Mobile Menu Toggle
function initMobileMenu() {
    // Support both old and new header structures
    const mobileToggle = document.querySelector('.mobile-menu-toggle, .modern-toggle');
    const navMenu = document.querySelector('.nav-menu, .modern-nav-menu');
    const body = document.body;

    if (mobileToggle && navMenu) {
        // Add accessibility attributes
        mobileToggle.setAttribute('aria-expanded', 'false');
        mobileToggle.setAttribute('aria-controls', 'navigation-menu');
        navMenu.setAttribute('id', 'navigation-menu');

        mobileToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isActive = mobileToggle.classList.contains('active');

            mobileToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            body.classList.toggle('menu-open');

            // Update accessibility attributes
            mobileToggle.setAttribute('aria-expanded', !isActive);

            // Animate toggle lines for modern header
            if (mobileToggle.classList.contains('modern-toggle')) {
                const lines = mobileToggle.querySelectorAll('.toggle-line');
                if (!isActive) { // Menu is opening
                    lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    lines[1].style.opacity = '0';
                    lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else { // Menu is closing
                    lines[0].style.transform = 'none';
                    lines[1].style.opacity = '1';
                    lines[2].style.transform = 'none';
                }
            }

            // Provide haptic feedback on mobile devices
            if ('vibrate' in navigator) {
                navigator.vibrate(50);
            }
        });

        // Handle mobile dropdown functionality
        const mobileDropdowns = navMenu.querySelectorAll('.dropdown');
        console.log('Found mobile dropdowns:', mobileDropdowns.length);

        mobileDropdowns.forEach((dropdown, index) => {
            const dropdownLink = dropdown.querySelector('.nav-link');
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');

            console.log(`Dropdown ${index}:`, {
                dropdown: dropdown,
                link: dropdownLink,
                menu: dropdownMenu
            });

            if (dropdownLink && dropdownMenu) {
                dropdownLink.addEventListener('click', function(e) {
                    console.log('Dropdown clicked, window width:', window.innerWidth);

                    // Only handle dropdown on mobile
                    if (window.innerWidth <= 767) {
                        e.preventDefault();
                        e.stopPropagation();

                        console.log('Mobile dropdown toggle triggered');

                        // Toggle this dropdown
                        dropdown.classList.toggle('active');

                        // Close other dropdowns
                        mobileDropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });

                        // Update ARIA attributes
                        const isExpanded = dropdown.classList.contains('active');
                        dropdownLink.setAttribute('aria-expanded', isExpanded);

                        console.log('Dropdown active state:', isExpanded);

                        // Provide haptic feedback
                        if ('vibrate' in navigator) {
                            navigator.vibrate(30);
                        }
                    }
                });
            } else {
                console.warn(`Dropdown ${index} missing elements:`, {
                    hasLink: !!dropdownLink,
                    hasMenu: !!dropdownMenu
                });
            }
        });

        // Close menu when clicking on non-dropdown links
        const navLinks = navMenu.querySelectorAll('a:not(.dropdown .nav-link)');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');

                // Update accessibility attributes
                mobileToggle.setAttribute('aria-expanded', 'false');

                // Reset toggle animation
                if (mobileToggle.classList.contains('modern-toggle')) {
                    const lines = mobileToggle.querySelectorAll('.toggle-line');
                    lines[0].style.transform = 'none';
                    lines[1].style.opacity = '1';
                    lines[2].style.transform = 'none';
                }
            });
        });

        // Close dropdown menu items when clicked
        const dropdownLinks = navMenu.querySelectorAll('.dropdown-menu a');
        dropdownLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');

                // Close all dropdowns
                mobileDropdowns.forEach(dropdown => {
                    dropdown.classList.remove('active');
                });

                // Update accessibility attributes
                mobileToggle.setAttribute('aria-expanded', 'false');

                // Reset toggle animation
                if (mobileToggle.classList.contains('modern-toggle')) {
                    const lines = mobileToggle.querySelectorAll('.toggle-line');
                    lines[0].style.transform = 'none';
                    lines[1].style.opacity = '1';
                    lines[2].style.transform = 'none';
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');

                // Update accessibility attributes
                mobileToggle.setAttribute('aria-expanded', 'false');

                // Reset toggle animation
                if (mobileToggle.classList.contains('modern-toggle')) {
                    const lines = mobileToggle.querySelectorAll('.toggle-line');
                    lines[0].style.transform = 'none';
                    lines[1].style.opacity = '1';
                    lines[2].style.transform = 'none';
                }
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');
                mobileToggle.setAttribute('aria-expanded', 'false');

                // Reset toggle animation
                if (mobileToggle.classList.contains('modern-toggle')) {
                    const lines = mobileToggle.querySelectorAll('.toggle-line');
                    lines[0].style.transform = 'none';
                    lines[1].style.opacity = '1';
                    lines[2].style.transform = 'none';
                }

                // Return focus to toggle button
                mobileToggle.focus();
            }
        });
    }
}

// Search Functionality
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (!searchInput || !searchSuggestions) return;

    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            searchSuggestions.classList.remove('active');
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetchSearchSuggestions(query);
        }, 300);
    });

    searchInput.addEventListener('focus', function() {
        if (this.value.trim().length >= 2) {
            searchSuggestions.classList.add('active');
        }
    });

    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
            searchSuggestions.classList.remove('active');
        }
    });
}

// Fetch search suggestions
function fetchSearchSuggestions(query) {
    // Mock search suggestions - in real implementation, this would be an API call
    const mockSuggestions = [
        'Rotavator',
        'Tractor',
        'Seed Drill',
        'Cultivator',
        'Harvester',
        'Sprayer',
        'Plough',
        'Thresher',
        'Disc Harrow',
        'Power Tiller'
    ];

    const filteredSuggestions = mockSuggestions.filter(item => 
        item.toLowerCase().includes(query.toLowerCase())
    );

    displaySearchSuggestions(filteredSuggestions, query);
}

// Display search suggestions
function displaySearchSuggestions(suggestions, query) {
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (suggestions.length === 0) {
        searchSuggestions.innerHTML = '<div class="suggestion-item">No suggestions found</div>';
    } else {
        searchSuggestions.innerHTML = suggestions.map(suggestion => 
            `<div class="suggestion-item" onclick="selectSuggestion('${suggestion}')">${suggestion}</div>`
        ).join('');
    }
    
    searchSuggestions.classList.add('active');
}

// Select search suggestion
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    searchInput.value = suggestion;
    searchSuggestions.classList.remove('active');
    
    // Redirect to products page with search query
    window.location.href = `products.html?search=${encodeURIComponent(suggestion)}`;
}

// Load featured products
async function initFeaturedProducts() {
    const featuredContainer = document.getElementById('featuredProducts');
    if (!featuredContainer) return;

    try {
        // Load products from JSON file
        const response = await fetch('data/products.json');
        const data = await response.json();
        const allProducts = data.products;

        // Show all 4 products as featured
        renderProducts(allProducts, featuredContainer);
    } catch (error) {
        console.error('Error loading featured products:', error);
        // Fallback to show a message
        featuredContainer.innerHTML = `
            <div class="no-products">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Featured products will be available soon</h3>
                <p>Please check back later or browse our product catalog</p>
                <a href="products.html" class="btn btn-primary">View All Products</a>
            </div>
        `;
    }
}

// Render modern 2025 products with expand/collapse functionality (price-free)
function renderProducts(products, container) {
    container.innerHTML = products.map(product => `
        <div class="product-card modern-card" data-product-id="${product.id}">
            <div class="product-image">
                <div class="image-gallery">
                    <img src="${product.image}" alt="${product.name}" loading="lazy"
                         onerror="this.src='images/placeholder-product.jpg'" class="main-image">
                    ${product.images && product.images.length > 1 ? `
                        <div class="image-thumbnails">
                            ${product.images.slice(0, 3).map((img, index) => `
                                <img src="${img}" alt="${product.name} ${index + 1}"
                                     class="thumbnail ${index === 0 ? 'active' : ''}"
                                     onclick="changeMainImage('${img}', this)"
                                     onerror="this.style.display='none'">
                            `).join('')}
                            ${product.images.length > 3 ? `
                                <div class="more-images" onclick="openImageGallery(${product.id})">
                                    +${product.images.length - 3}
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
                ${product.badge ? `<span class="product-badge modern-badge">${product.badge}</span>` : ''}
                <div class="card-overlay"></div>
            </div>
            <div class="product-info">
                <!-- Compact content (always visible) -->
                <div class="product-info-compact">
                    <div class="product-brand modern-brand">${product.brand}</div>
                    <h3 class="product-title modern-title">${product.name}</h3>

                    <!-- Modern primary CTA -->
                    <div class="product-actions-compact">
                        <button class="btn btn-primary btn-quote modern-cta" onclick="requestQuote(${product.id})">
                            <i class="fas fa-envelope"></i>
                            <span>Get Quote</span>
                        </button>
                        <a href="product-detail.html?id=${product.id}" class="btn btn-secondary btn-compact modern-secondary">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                    </div>
                </div>

                <!-- Modern Show More/Less button -->
                <button class="show-more-btn modern-expand" onclick="toggleProductCard(${product.id})">
                    <span class="show-more-text">Show More Details</span>
                    <i class="fas fa-chevron-down"></i>
                </button>

                <!-- Modern expandable content (hidden by default) -->
                <div class="product-info-expandable modern-expandable">
                    <p class="product-description modern-description">${product.description}</p>

                    ${product.rating ? `
                        <div class="product-rating-section modern-rating">
                            <div class="rating-container">
                                <div class="stars">
                                    ${generateStars(product.rating)}
                                </div>
                                <span class="rating-text">${product.rating} (${product.reviews || 0} reviews)</span>
                            </div>
                        </div>
                    ` : ''}

                    <!-- Modern Features -->
                    <div class="features-section">
                        <h4 class="section-title">
                            <i class="fas fa-check-circle"></i>
                            Key Features
                        </h4>
                        <ul class="product-features modern-features">
                            ${product.features.slice(0, 5).map(feature => `
                                <li><i class="fas fa-check"></i> <span>${feature}</span></li>
                            `).join('')}
                        </ul>
                    </div>

                    <!-- Modern Specifications -->
                    ${product.specifications ? `
                        <div class="specs-section">
                            <h4 class="section-title">
                                <i class="fas fa-cog"></i>
                                Specifications
                            </h4>
                            <div class="specs-grid">
                                ${Object.entries(product.specifications).slice(0, 4).map(([key, value]) => `
                                    <div class="spec-item">
                                        <span class="spec-label">${key}</span>
                                        <span class="spec-value">${value}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    ${product.warranty ? `
                        <div class="warranty-section">
                            <div class="warranty-info modern-warranty">
                                <i class="fas fa-shield-alt"></i>
                                <span>${product.warranty}</span>
                            </div>
                        </div>
                    ` : ''}

                    ${product.certifications ? `
                        <div class="certifications-section">
                            <h4 class="section-title">
                                <i class="fas fa-certificate"></i>
                                Certifications
                            </h4>
                            <div class="certifications-grid">
                                ${product.certifications.map(cert => `
                                    <span class="cert-badge modern-cert">${cert}</span>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <!-- Modern full actions -->
                    <div class="product-actions-full modern-actions">
                        <button class="btn btn-primary btn-large modern-primary" onclick="requestQuote(${product.id})">
                            <i class="fas fa-envelope"></i>
                            <span>Get Detailed Quote</span>
                        </button>
                        <a href="product-detail.html?id=${product.id}" class="btn btn-secondary btn-large modern-secondary">
                            <i class="fas fa-eye"></i>
                            <span>View Full Details</span>
                        </a>
                        <button class="btn btn-outline btn-large modern-outline" onclick="addToComparison(${product.id})" title="Add to Compare">
                            <i class="fas fa-balance-scale"></i>
                            <span>Compare</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    // Initialize lazy loading for newly added images
    initLazyLoading();

    // Initialize keyboard accessibility for product cards
    initProductCardAccessibility();
}

// Initialize keyboard accessibility for product cards
function initProductCardAccessibility() {
    document.addEventListener('keydown', function(e) {
        // Handle Enter and Space key for show more buttons
        if ((e.key === 'Enter' || e.key === ' ') && e.target.classList.contains('show-more-btn')) {
            e.preventDefault();
            e.target.click();
        }

        // Handle Escape key to collapse expanded cards
        if (e.key === 'Escape') {
            const expandedCards = document.querySelectorAll('.product-card.expanded');
            expandedCards.forEach(card => {
                const productId = card.getAttribute('data-product-id');
                if (productId) {
                    toggleProductCard(productId);
                }
            });
        }
    });
}

// Request quote functionality
function requestQuote(productId) {
    // Store product ID for quote form
    localStorage.setItem('quoteProductId', productId);

    // Redirect to contact page with quote parameter
    window.location.href = 'contact.html?action=quote&product=' + productId;
}

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let starsHTML = '';

    // Full stars
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }

    // Half star
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }

    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }

    return starsHTML;
}

// Enhanced toggle product card expand/collapse functionality
function toggleProductCard(productId) {
    const productCard = document.querySelector(`[data-product-id="${productId}"]`);
    const showMoreBtn = productCard.querySelector('.show-more-btn');
    const showMoreText = showMoreBtn.querySelector('.show-more-text');
    const showMoreIcon = showMoreBtn.querySelector('i');

    // Prevent multiple rapid clicks
    if (productCard.classList.contains('expanding')) {
        return;
    }

    // Add expanding state
    productCard.classList.add('expanding');

    if (productCard.classList.contains('expanded')) {
        // Collapse card
        productCard.classList.remove('expanded');
        showMoreBtn.classList.remove('expanded');
        showMoreText.textContent = 'Show More Details';
        showMoreIcon.style.transform = 'rotate(0deg)';

        // Smooth scroll to card top after collapse
        setTimeout(() => {
            productCard.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'nearest'
            });
            productCard.classList.remove('expanding');
        }, 300);

        // Track analytics (if needed)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'product_card_collapse', {
                'product_id': productId
            });
        }
    } else {
        // Expand card
        productCard.classList.add('expanded');
        showMoreBtn.classList.add('expanded');
        showMoreText.textContent = 'Show Less Details';
        showMoreIcon.style.transform = 'rotate(180deg)';

        // Remove expanding state after animation
        setTimeout(() => {
            productCard.classList.remove('expanding');
        }, 400);

        // Track analytics (if needed)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'product_card_expand', {
                'product_id': productId
            });
        }
    }

    // Provide haptic feedback on mobile devices
    if ('vibrate' in navigator) {
        navigator.vibrate(50);
    }
}

// Change main image in product card
function changeMainImage(imageSrc, thumbnailElement) {
    const productCard = thumbnailElement.closest('.product-card');
    const mainImage = productCard.querySelector('.main-image');
    const thumbnails = productCard.querySelectorAll('.thumbnail');

    // Update main image
    mainImage.src = imageSrc;

    // Update active thumbnail
    thumbnails.forEach(thumb => thumb.classList.remove('active'));
    thumbnailElement.classList.add('active');
}

// Open image gallery modal
function openImageGallery(productId) {
    // This would open a modal with all product images
    // For now, redirect to product detail page
    window.location.href = `product-detail.html?id=${productId}#gallery`;
}

// Add to comparison functionality
function addToComparison(productId) {
    // This would integrate with the comparison system
    if (typeof window.addToComparison === 'function') {
        const success = window.addToComparison({ id: productId });
        if (success) {
            showNotification('Product added to comparison', 'success');
        } else {
            showNotification('Maximum 3 products can be compared', 'warning');
        }
    } else {
        showNotification('Comparison feature will be available soon', 'info');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.category-card, .product-card, .testimonial-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

// Validate form
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        const errorElement = field.parentNode.querySelector('.error-message');
        
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else if (field.type === 'email' && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        } else if (field.type === 'tel' && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
        } else {
            hideFieldError(field);
        }
    });
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('error');
    
    let errorElement = field.parentNode.querySelector('.error-message');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
}

// Hide field error
function hideFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.error-message');
    if (errorElement) {
        errorElement.remove();
    }
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation
function isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}

// Initialize tooltips
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// Show tooltip
function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    setTimeout(() => tooltip.classList.add('show'), 10);
}

// Hide tooltip
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Enhanced lazy loading for images with loading states
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;

                    // Add loading state
                    img.classList.add('loading');

                    // Create a new image to preload
                    const newImg = new Image();
                    newImg.onload = function() {
                        img.src = this.src;
                        img.classList.remove('lazy', 'loading');
                        img.classList.add('loaded');
                    };
                    newImg.onerror = function() {
                        img.classList.remove('lazy', 'loading');
                        img.src = 'images/placeholder-product.jpg';
                    };
                    newImg.src = img.src;

                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('lazy');
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            img.classList.add('loaded');
        });
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Show loading state
function showLoading(element) {
    element.classList.add('loading');
    const spinner = document.createElement('div');
    spinner.className = 'spinner';
    element.appendChild(spinner);
}

// Hide loading state
function hideLoading(element) {
    element.classList.remove('loading');
    const spinner = element.querySelector('.spinner');
    if (spinner) {
        spinner.remove();
    }
}

// Force list view on mobile devices with improved error handling
function initMobileProductView() {
    function setMobileView() {
        try {
            console.log('Setting mobile view, window width:', window.innerWidth);

            if (window.innerWidth <= 767) {
                const productsGrid = document.getElementById('productsGrid');
                const viewButtons = document.querySelectorAll('.view-btn');
                const viewOptions = document.querySelector('.view-options');

                console.log('Mobile view elements found:', {
                    productsGrid: !!productsGrid,
                    viewButtons: viewButtons.length,
                    viewOptions: !!viewOptions
                });

                if (productsGrid) {
                    // Force list view
                    productsGrid.className = 'products-list';
                    console.log('Forced list view on products grid');

                    // Update view buttons if they exist
                    if (viewButtons.length > 0) {
                        viewButtons.forEach(btn => {
                            btn.classList.remove('active');
                            if (btn.dataset.view === 'list') {
                                btn.classList.add('active');
                                console.log('Set list view button as active');
                            }
                        });
                    } else {
                        console.log('No view buttons found - this is normal for some pages');
                    }
                } else {
                    console.log('Products grid not found - this is normal for non-product pages');
                }

                // Hide view options on mobile
                if (viewOptions) {
                    viewOptions.style.display = 'none';
                    console.log('Hidden view options on mobile');
                } else {
                    console.log('View options not found - this is normal for some pages');
                }
            } else {
                // Show view options on desktop
                const viewOptions = document.querySelector('.view-options');
                if (viewOptions) {
                    viewOptions.style.display = 'flex';
                    console.log('Shown view options on desktop');
                }
            }
        } catch (error) {
            console.error('Error in setMobileView:', error);
        }
    }

    // Set initial view with error handling
    try {
        setMobileView();
    } catch (error) {
        console.error('Error in initial mobile view setup:', error);
    }

    // Update on window resize with debouncing
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            try {
                setMobileView();
            } catch (error) {
                console.error('Error in resize mobile view setup:', error);
            }
        }, 250);
    });

    // Also run after a short delay to catch dynamically loaded content
    setTimeout(() => {
        try {
            setMobileView();
        } catch (error) {
            console.error('Error in delayed mobile view setup:', error);
        }
    }, 1000);
}

// Initialize all functionality when DOM is loaded with improved error handling
document.addEventListener('DOMContentLoaded', function() {
    console.log('Main.js initializing...');

    // Initialize components with error handling
    const initComponents = [
        { name: 'Mobile Menu', fn: initMobileMenu },
        { name: 'Mobile Product View', fn: initMobileProductView },
        { name: 'Search', fn: initSearch },
        { name: 'Featured Products', fn: initFeaturedProducts },
        { name: 'Scroll Animations', fn: initScrollAnimations },
        { name: 'Form Validation', fn: initFormValidation },
        { name: 'Lazy Loading', fn: initLazyLoading }
    ];

    initComponents.forEach(component => {
        try {
            console.log(`Initializing ${component.name}...`);
            component.fn();
            console.log(`${component.name} initialized successfully`);
        } catch (error) {
            console.error(`Error initializing ${component.name}:`, error);
            // Continue with other components even if one fails
        }
    });

    console.log('Main.js initialization completed');
});
